package com.fxiaoke.analyzer.service;

import com.fxiaoke.analyzer.bean.AnalyzedWords;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class AnalyzerServiceTest {

  private static final AnalyzerService service = new AnalyzerService();

  @BeforeAll
  static void setUp() throws Exception {
    service.init();
  }

//  @Test
//  void testChinaAnalyze() {
//    AnalyzedWords analyze = service.analyze("Apple Asia LLC, Taiwan Branch");
//    assertEquals("Asia", analyze.getLocation());
//    assertEquals("LLC, Taiwan Branch", analyze.getSuffix());
//    assertEquals("Apple", analyze.getKeyword());
//  }

  @Test
  void testInternationalAnalyze() {
    AnalyzedWords analyze = service.analyze("Apple Asia LLC, Taiwan Branch");
    assertEquals("Asia", analyze.getLocation());
    assertEquals("LLC, Taiwan Branch", analyze.getSuffix());
    assertEquals("Apple", analyze.getKeyword());

    analyze = service.analyze("Apple South Asia (Thailand) Limited");
    assertEquals("South Asia (Thailand)", analyze.getLocation());
    assertEquals("Limited", analyze.getSuffix());
    assertEquals("Apple", analyze.getKeyword());

    analyze = service.analyze("Apple Inc.");
    assertEquals("", analyze.getLocation());
    assertEquals("Inc.", analyze.getSuffix());
    assertEquals("Apple", analyze.getKeyword());

    analyze = service.analyze("Apple Japan, Inc");
    assertEquals("Japan", analyze.getLocation());
    assertEquals("Inc", analyze.getSuffix());
    assertEquals("Apple", analyze.getKeyword());

    analyze = service.analyze("Apple South Asia Pte. Ltd.");
    assertEquals("South Asia", analyze.getLocation());
    assertEquals("Pte. Ltd.", analyze.getSuffix());
    assertEquals("Apple", analyze.getKeyword());


  }


}