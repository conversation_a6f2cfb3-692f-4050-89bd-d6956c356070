package com.fxiaoke.analyzer.service;

import com.fxiaoke.analyzer.bean.AnalyzedWords;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.junit.runner.RunWith;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * 测试分词效果
 * Created by lirui on 2017-05-24 18:42.
 * Converted from Groovy Spock to Java JUnit 5
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test.xml")
class AnalyzerServiceTest {

  @Autowired
  private AnalyzerService analyzer;

  @Test
  void testAnalyzeAppleInc() {
    AnalyzedWords words = analyzer.analyze("Apple Inc.");
    assertEquals("Apple", words.getKeyword());
    assertEquals("", words.getLocation());
    assertEquals("Inc.", words.getSuffix());
  }

  @Test
  void testAnalyze中国农业银行河南() {
    AnalyzedWords words = analyzer.analyze("中国农业银行河南");
    assertEquals("中国农业银行", words.getKeyword());
    assertEquals("河南", words.getLocation());
    assertEquals("", words.getSuffix());
  }

  @Test
  void testAnalyze上海巴黎三城眼镜有限公司第八十二分公司() {
    AnalyzedWords words = analyzer.analyze("上海巴黎三城眼镜有限公司第八十二分公司");
    assertEquals("巴黎三城眼镜", words.getKeyword());
    assertEquals("上海", words.getLocation());
    assertEquals("有限公司第八十二分公司", words.getSuffix());
  }

  @Test
  void testAnalyze乌苏市夹河子乡阿克友力养殖农民专业合作社() {
    AnalyzedWords words = analyzer.analyze("乌苏市夹河子乡阿克友力养殖农民专业合作社");
    assertEquals("夹河子乡阿克友力养殖", words.getKeyword());
    assertEquals("乌苏市", words.getLocation());
    assertEquals("农民专业合作社", words.getSuffix());
  }

  @Test
  void testAnalyze北京圆明园汽车贸易中心() {
    AnalyzedWords words = analyzer.analyze("北京圆明园汽车贸易中心");
    assertEquals("圆明园汽车", words.getKeyword());
    assertEquals("北京", words.getLocation());
    assertEquals("贸易中心", words.getSuffix());
  }

  @Test
  void testAnalyze北京大同华眼镜有限责任公司() {
    AnalyzedWords words = analyzer.analyze("北京大同华眼镜有限责任公司");
    assertEquals("大同华眼镜", words.getKeyword());
    assertEquals("北京", words.getLocation());
    assertEquals("有限责任公司", words.getSuffix());
  }

  @Test
  void testAnalyze吉林市长春路油脂商店() {
    AnalyzedWords words = analyzer.analyze("吉林市长春路油脂商店");
    assertEquals("长春路油脂", words.getKeyword());
    assertEquals("吉林市", words.getLocation());
    assertEquals("商店", words.getSuffix());
  }

  @Test
  void testAnalyze徐州中国矿业大学经营实业总公司物资经销处() {
    AnalyzedWords words = analyzer.analyze("徐州中国矿业大学经营实业总公司物资经销处");
    assertEquals("中国矿业大学", words.getKeyword());
    assertEquals("徐州", words.getLocation());
    assertEquals("经营实业总公司物资经销处", words.getSuffix());
  }

  @Test
  void testAnalyze徐州市城区供销合作总社拾屯面粉厂() {
    AnalyzedWords words = analyzer.analyze("徐州市城区供销合作总社拾屯面粉厂");
    assertEquals("供销合作总社拾屯", words.getKeyword());
    assertEquals("徐州市城区", words.getLocation());
    assertEquals("面粉厂", words.getSuffix());
  }

  @Test
  void testAnalyze河南阿里九九贸易有限公司() {
    AnalyzedWords words = analyzer.analyze("河南阿里九九贸易有限公司");
    assertEquals("阿里九九", words.getKeyword());
    assertEquals("河南", words.getLocation());
    assertEquals("贸易有限公司", words.getSuffix());
  }

  @Test
  void testAnalyze甲骨文中国软件服务有限公司北京分公司() {
    AnalyzedWords words = analyzer.analyze("甲骨文（中国）软件服务有限公司北京分公司");
    assertEquals("甲骨文", words.getKeyword());
    assertEquals("中国 北京", words.getLocation());
    assertEquals("软件服务有限公司分公司", words.getSuffix());
  }

  @Test
  void testAnalyze苹果电脑贸易上海有限公司() {
    AnalyzedWords words = analyzer.analyze("苹果电脑贸易（上海）有限公司");
    assertEquals("苹果", words.getKeyword());
    assertEquals("上海", words.getLocation());
    assertEquals("电脑贸易有限公司", words.getSuffix());
  }

  @Test
  void testAnalyze中国移动通信研究院() {
    AnalyzedWords words = analyzer.analyze("中国移动通信研究院");
    assertEquals("中国移动通信", words.getKeyword());
    assertEquals("", words.getLocation());
    assertEquals("研究院", words.getSuffix());
  }

  @Test
  void testAnalyze中国移动国际信息港建设中心() {
    AnalyzedWords words = analyzer.analyze("中国移动国际信息港建设中心");
    assertEquals("中国移动", words.getKeyword());
    assertEquals("", words.getLocation());
    assertEquals("国际信息港建设中心", words.getSuffix());
  }

  @Test
  void testAnalyze中国移动集团广东有限公司() {
    AnalyzedWords words = analyzer.analyze("中国移动集团广东有限公司");
    assertEquals("中国移动集团", words.getKeyword());
    assertEquals("广东", words.getLocation());
    assertEquals("有限公司", words.getSuffix());
  }

  @Test
  void testAnalyze法国国际作者和作曲者协会联合会北京代表处() {
    AnalyzedWords words = analyzer.analyze("法国国际作者和作曲者协会联合会北京代表处");
    assertEquals("法国国际作者和作曲者协会联合会", words.getKeyword());
    assertEquals("北京", words.getLocation());
    assertEquals("代表处", words.getSuffix());
  }

  @Test
  void testAnalyze珠海法国化妆品厂有限公司成都经营部() {
    AnalyzedWords words = analyzer.analyze("珠海（法国）化妆品厂有限公司成都经营部");
    assertEquals("（法国）化妆品厂", words.getKeyword());
    assertEquals("珠海", words.getLocation());
    assertEquals("有限公司成都经营部", words.getSuffix());
  }

    @Test
    void testAnalyze阿尔法国际有限公司() {
      AnalyzedWords words = analyzer.analyze("阿尔法国际有限公司");
      assertEquals("阿尔法", words.getKeyword());
      assertEquals("", words.getLocation());
      assertEquals("国际有限公司", words.getSuffix());
    }

    @Test
    void testAnalyze中国邮政集团公司广东省雷州市西湖报刊亭() {
      AnalyzedWords words = analyzer.analyze("中国邮政集团公司广东省雷州市西湖报刊亭");
      assertEquals("中国邮政", words.getKeyword());
      assertEquals("广东省雷州市", words.getLocation());
      assertEquals("集团公司西湖报刊亭", words.getSuffix());
    }

    @Test
    void testAnalyze中国第十冶金建设公司柳州经贸公司() {
      AnalyzedWords words = analyzer.analyze("中国第十冶金建设公司柳州经贸公司");
      assertEquals("中国第十冶金建设", words.getKeyword());
      assertEquals("柳州", words.getLocation());
      assertEquals("公司 经贸公司", words.getSuffix());
    }

    @Test
    void testAnalyze信元担保中国有限公司遵义分公司() {
      AnalyzedWords words = analyzer.analyze("信元担保（中国）有限公司遵义分公司");
      assertEquals("信元担保", words.getKeyword());
      assertEquals("中国 遵义", words.getLocation());
      assertEquals("有限公司分公司", words.getSuffix());
    }

    @Test
    void testAnalyze中国太平洋人寿保险股份有限公司淮安中心支公司洪泽县黄集营销服务部() {
      AnalyzedWords words = analyzer.analyze("中国太平洋人寿保险股份有限公司淮安中心支公司洪泽县黄集营销服务部");
      assertEquals("中国太平洋人寿保险", words.getKeyword());
      assertEquals("淮安", words.getLocation());
      assertEquals("股份有限公司中心支公司洪泽县黄集营销服务部", words.getSuffix());
    }

    @Test
    void testAnalyze酪悦轩尼诗帝亚吉欧洋酒上海有限公司成都办事处() {
      AnalyzedWords words = analyzer.analyze("酪悦轩尼诗帝亚吉欧洋酒（上海）有限公司成都办事处");
      assertEquals("酪悦轩尼诗帝亚吉欧洋酒", words.getKeyword());
      assertEquals("上海", words.getLocation());
      assertEquals("有限公司成都办事处", words.getSuffix());
    }

    @Test
    void testAnalyze中国包装进出口长春公司大连办事处() {
      AnalyzedWords words = analyzer.analyze("中国包装进出口长春公司大连办事处");
      assertEquals("中国包装进出口", words.getKeyword());
      assertEquals("长春", words.getLocation());
      assertEquals("公司大连办事处", words.getSuffix());
    }

    @Test
    void testAnalyze韩国久久有限公司上海代表处() {
      AnalyzedWords words = analyzer.analyze("韩国久久有限公司上海代表处");
      assertEquals("韩国久久", words.getKeyword());
      assertEquals("上海", words.getLocation());
      assertEquals("有限公司 代表处", words.getSuffix());
    }

    @Test
    void testAnalyze联想科技上海分公司() {
      AnalyzedWords words = analyzer.analyze("联想科技上海分公司");
      assertEquals("联想科技", words.getKeyword());
      assertEquals("上海", words.getLocation());
      assertEquals("分公司", words.getSuffix());
    }

    @Test
    void testAnalyze朝阳市朝阳市场() {
      AnalyzedWords words = analyzer.analyze("朝阳市朝阳市场");
      assertEquals("朝阳市场", words.getKeyword());
      assertEquals("朝阳市", words.getLocation());
      assertEquals("", words.getSuffix());
    }

    @Test
    void testAnalyze联想科技() {
      AnalyzedWords words = analyzer.analyze("联想科技");
      assertEquals("联想", words.getKeyword());
      assertEquals("", words.getLocation());
      assertEquals("科技", words.getSuffix());
    }

    @Test
    void testAnalyze金鑫大药房() {
      AnalyzedWords words = analyzer.analyze("金鑫大药房");
      assertEquals("金鑫", words.getKeyword());
      assertEquals("", words.getLocation());
      assertEquals("大药房", words.getSuffix());
    }

    @Test
    void testAnalyze天天网络() {
      AnalyzedWords words = analyzer.analyze("天天网络");
      assertEquals("天天", words.getKeyword());
      assertEquals("", words.getLocation());
      assertEquals("网络", words.getSuffix());
      }

    @Test
    void testAnalyze华大酒店() {
      AnalyzedWords words = analyzer.analyze("华大酒店");
      assertEquals("华大", words.getKeyword());
      assertEquals("", words.getLocation());
      assertEquals("酒店", words.getSuffix());
    }

    @Test
    void testAnalyze粉越西贡() {
      AnalyzedWords words = analyzer.analyze("粉越西贡");
      assertEquals("粉越西贡", words.getKeyword());
      assertEquals("", words.getLocation());
      assertEquals("", words.getSuffix());
    }
}