package com.fxiaoke.analyzer.bean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
public class AnalyzedWords {
  private String word;
  private String location = "";
  private String keyword = "";
  private String suffix = "";

  public AnalyzedWords(String word) {
    this.word = word;
    this.keyword = word;
  }
}
