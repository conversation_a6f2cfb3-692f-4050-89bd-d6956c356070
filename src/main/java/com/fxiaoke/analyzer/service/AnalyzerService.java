package com.fxiaoke.analyzer.service;

import com.fxiaoke.analyzer.bean.AnalyzedWords;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;
import lombok.extern.slf4j.Slf4j;
import org.ahocorasick.trie.Emit;
import org.ahocorasick.trie.Token;
import org.ahocorasick.trie.Trie;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 分析搜索词，识别地域和公司后缀名
 * Created by ang on 2017/5/19.
 */
@Service
@Slf4j
public class AnalyzerService {
  private Multimap<String, String> city2code = HashMultimap.create();
  private final Pattern china = Pattern.compile("(中国.*(?:银行|集团|保险|证券|投资|金融|石油|天然气|解放军|物流|通信|进出口|石化|化工|电信|铁路|铁塔|水电|飞机|航空|烟草|联通|移动|铁通))((?:股份)?(?:有限)?(?:责任)?公司)?");
  private final Pattern around = Pattern.compile("[（(](.{1,4})[)）]");
  private Trie cityTrie;
  private Trie companyTrie;
  private Trie scopeTrie;

  @PostConstruct
  void init() {
    ConfigFactory.getConfig("company-suffix", config -> this.companyTrie = Trie.builder().ignoreOverlaps().addKeywords(config.getLines()).build());

    ConfigFactory.getConfig("china-company-scope", config -> this.scopeTrie = Trie.builder().ignoreOverlaps().addKeywords(config.getLines()).build());

    ConfigFactory.getConfig("china-city-code", conf -> {
      Pattern p1 = Pattern.compile("(?:自治区|特别行政区|自治县|自治州|矿区|林区|堂区)$");
      Pattern p2 = Pattern.compile("[省|市|区|县|州|盟]$");
      Multimap<String, String> codes = HashMultimap.create();
      Splitter splitter = Splitter.on(CharMatcher.anyOf(",，")).trimResults().omitEmptyStrings();
      Trie.TrieBuilder builder = Trie.builder().ignoreOverlaps();
      conf.getAll().forEach((code, value) -> {
        List<String> cities = splitter.splitToList(value);
        cities.forEach(city -> {
          builder.addKeyword(city);
          codes.put(city, code);
          if (city.length() < 3) {
            return;
          }
          if (p1.matcher(city).find()) {
          } else if (city.endsWith("地区")) {
            String abbr = city.substring(0, city.length() - 2);
            builder.addKeyword(abbr);
            codes.put(abbr, code);
          } else if (city.endsWith("回族区")) {
            String abbr = city.substring(0, city.length() - 3);
            builder.addKeyword(abbr);
            codes.put(abbr, code);
          } else if (city.endsWith("达斡尔族区")) {
            String abbr = city.substring(0, city.length() - 5);
            builder.addKeyword(abbr);
            codes.put(abbr, code);
          } else if (p2.matcher(city).find()) {
            String abbr = city.substring(0, city.length() - 1);
            builder.addKeyword(abbr);
            codes.put(abbr, code);
          }
        });
      });
      this.city2code = codes;
      this.cityTrie = builder.build();
    });
  }

  public AnalyzedWords analyze(String word) {
    AnalyzedWords words = doAnalyze(word);
    adjust(words);
    return words;
  }

  /**
   * 针对分词不恰当的地方做微调
   *
   * @param words 分词结果
   * @return
   */
  private void adjust(AnalyzedWords words) {
    String keyword = words.getKeyword();
    String suffix = words.getSuffix();
    if (keyword.endsWith("集团") && suffix.startsWith("公司")) {
      keyword = keyword.substring(0, keyword.length() - 2);
      suffix = "集团" + suffix;
      words.setKeyword(keyword);
      words.setSuffix(suffix);
    }
    if (Strings.isNullOrEmpty(words.getLocation())) {
      scanLocation(words);
    }
    if ("中国".equals(words.getLocation()) && words.getSuffix() != null) {
      Emit emit = cityTrie.firstMatch(suffix);
      if (emit != null) {
        words.setLocation("中国 " + emit.getKeyword());
        words.setSuffix(words.getSuffix().replace(emit.getKeyword(), ""));
      }
    }
    if (words.getKeyword().length() < 2) {
      words.setKeyword(words.getKeyword() + words.getSuffix());
      words.setSuffix("");
    }
    if (words.getSuffix().length() == 0) {
      keyword = words.getKeyword();
      Emit emit = scopeTrie.firstMatch(keyword);
      if (emit != null) {
        int start = emit.getStart();
        if (start > 1) {
          String prefix = keyword.substring(0, start);
          if (!"中国".equals(prefix)) {
            words.setKeyword(prefix);
            words.setSuffix(keyword.substring(start));
          }
        }
      }
    }
  }

  private void scanLocation(AnalyzedWords words) {
    String keyword = words.getKeyword();
    String suffix = words.getSuffix();
    if (!Strings.isNullOrEmpty(suffix) && !"分公司".equals(suffix)) {
      Emit emit = cityTrie.firstMatch(suffix);
      if (emit != null) {
        words.setLocation(emit.getKeyword());
        words.setSuffix(suffix.replace(emit.getKeyword(), " "));
      }
    } else {
      Emit emit = cityTrie.firstMatch(keyword);
      if (emit != null && emit.getStart() > 1) {
        words.setLocation(emit.getKeyword());
        words.setKeyword(keyword.substring(0, emit.getStart()));
        words.setSuffix(suffix + keyword.substring(emit.getEnd() + 1));
      }
    }
  }

  private AnalyzedWords doAnalyze(String word) {
    AnalyzedWords words = new AnalyzedWords(word);
    if (word.startsWith("中国")) {
      scanChina(words);
      return words;
    }
    // 首先解析公司名后缀
    extractCompanySuffix(words, word);
    if (word.length() < 4) {
      return words;
    }
    // 去掉后缀之后，解析地域信息
    Collection<Token> tokens = cityTrie.tokenize(words.getKeyword());
    if (tokens.size() < 2) {
      scanLocationIntern(words);
      return words;
    }
    Iterator<Token> it = tokens.iterator();
    Token first = it.next();
    Token second = it.next();
    if (!first.isMatch()) {
      // 如果开头不是地域词，直接返回
      scanLocationIntern(words);
      return words;
    }
    if (first.getEmit().getEnd() + 5 > words.getKeyword().length() && second.isMatch()) {
      // 第二个词也是地域词，判断归属关系
      Collection<String> codes = city2code.get(first.getFragment());
      if (codes.size() == 1) {
        // 二级城市的code码前2位，必然和上级城市的前2位相同
        // 比如：北京（110000），东城区（110101）
        String fCode = getCityCodePrefix(codes.iterator().next());
        if (city2code.get(second.getFragment()).stream().anyMatch(i -> i.startsWith(fCode))) {
          matchBeginLocation(words, second.getEmit().getEnd() + 1);
          return words;
        }
      }
    }
    matchBeginLocation(words, first.getEmit().getEnd() + 1);
    return words;
  }

  private void scanChina(AnalyzedWords words) {
    String word = words.getWord().replace("（集团）", "集团");
    Matcher matcher = china.matcher(word);
    if (matcher.find()) {
      words.setKeyword(matcher.group(1));
      String local = word.substring(matcher.end());
      StringBuilder suffix = new StringBuilder();
      StringBuilder location = new StringBuilder();
      // 例子：[中国移动集团][有限公司]广东省雷州市分公司
      String company = matcher.group(2);
      if (company != null) {
        suffix.append(company);
      }
      if (local.length() > 0) {
        // 例子：中国邮政集团公司[广东省雷州市]西湖报刊亭  中国包装进出口[长春]公司大连办事处
        // 只获取连续的前2个城市名称
        int count = 0;
        boolean add = true;
        for (Token token : cityTrie.tokenize(local)) {
          if (token.isMatch() && add && count++ < 2) {
            location.append(token.getFragment());
          } else {
            add = false;
            suffix.append(token.getFragment());
          }
          words.setLocation(location.toString());
        }
      }
      words.setSuffix(suffix.toString());
    } else {
      extractCompanySuffix(words, word);
    }
  }

  private void scanLocationIntern(AnalyzedWords words) {
    Matcher matcher = around.matcher(words.getKeyword());
    if (matcher.find()) {
      String city = matcher.group(1);
      if ("中国".equals(city) || cityTrie.containsMatch(city)) {
        words.setLocation(city);
        String word = words.getKeyword().substring(0, matcher.start());
        if (words.getSuffix() != null) {
          word += words.getSuffix();
          extractCompanySuffix(words, word);
        } else {
          words.setKeyword(words.getKeyword().substring(0, matcher.start()));
        }
      }
    }
  }

  private String getCityCodePrefix(String fCode) {
    if (fCode.endsWith("0000")) {
      return fCode.substring(0, 2);
    } else {
      return fCode.substring(0, 4);
    }
  }

  private void matchBeginLocation(AnalyzedWords words, final int pos) {
    String keyword = words.getKeyword();
    if (pos < keyword.length()) {
      int start = pos;
      if (("县市区").indexOf(keyword.charAt(pos)) != -1) {
        start++;
      } else if (keyword.startsWith("城区", pos) || keyword.startsWith("郊区", pos)) {
        // 例子： [徐州市城区]供销合作总社拾屯面粉厂
        start += 2;
      }
      words.setLocation(keyword.substring(0, start));
      words.setKeyword(keyword.substring(start));
    }
  }

  private void extractCompanySuffix(AnalyzedWords words, String word) {
    Emit emit = companyTrie.firstMatch(word);
    if (emit != null) {
      words.setKeyword(word.substring(0, emit.getStart()));
      words.setSuffix(word.substring(emit.getStart()));
    }
  }
}
